// Simple expense split calculator
let people = [];
let expenses = [];

// Add person to the group
function addPerson() {
    const nameInput = document.getElementById('personName');
    const name = nameInput.value.trim();
    
    if (name === '') {
        alert('Please enter a name!');
        return;
    }
    
    if (people.includes(name)) {
        alert('This person is already added!');
        return;
    }
    
    people.push(name);
    nameInput.value = '';
    updatePeopleDisplay();
    updateWhoPaidDropdown();
}

// Remove person from group
function removePerson(name) {
    people = people.filter(person => person !== name);
    // Also remove their expenses
    expenses = expenses.filter(expense => expense.paidBy !== name);
    updatePeopleDisplay();
    updateWhoPaidDropdown();
    updateExpensesDisplay();
    calculateResults();
}

// Update the people display
function updatePeopleDisplay() {
    const peopleList = document.getElementById('peopleList');
    
    if (people.length === 0) {
        peopleList.innerHTML = '<p class="empty-state">No people added yet.</p>';
        return;
    }
    
    peopleList.innerHTML = people.map(person => `
        <div class="person-tag">
            ${person}
            <button class="remove-btn" onclick="removePerson('${person}')">×</button>
        </div>
    `).join('');
}

// Update who paid dropdown
function updateWhoPaidDropdown() {
    const whoPaidSelect = document.getElementById('whoPaid');
    whoPaidSelect.innerHTML = '<option value="">Who paid?</option>';
    
    people.forEach(person => {
        whoPaidSelect.innerHTML += `<option value="${person}">${person}</option>`;
    });
}

// Add expense
function addExpense() {
    const desc = document.getElementById('expenseDesc').value.trim();
    const amount = parseFloat(document.getElementById('expenseAmount').value);
    const paidBy = document.getElementById('whoPaid').value;
    
    if (!desc) {
        alert('Please enter what the expense was for!');
        return;
    }
    
    if (!amount || amount <= 0) {
        alert('Please enter a valid amount!');
        return;
    }
    
    if (!paidBy) {
        alert('Please select who paid!');
        return;
    }
    
    const expense = {
        id: Date.now(),
        description: desc,
        amount: amount,
        paidBy: paidBy,
        date: new Date().toLocaleDateString()
    };
    
    expenses.push(expense);
    
    // Clear form
    document.getElementById('expenseDesc').value = '';
    document.getElementById('expenseAmount').value = '';
    document.getElementById('whoPaid').value = '';
    
    updateExpensesDisplay();
    calculateResults();
}

// Delete expense
function deleteExpense(id) {
    expenses = expenses.filter(expense => expense.id !== id);
    updateExpensesDisplay();
    calculateResults();
}

// Update expenses display
function updateExpensesDisplay() {
    const expensesList = document.getElementById('expensesList');
    const totalElement = document.getElementById('totalAmount');
    
    if (expenses.length === 0) {
        expensesList.innerHTML = '<p class="empty-state">No expenses yet. Add one above!</p>';
        totalElement.textContent = '0.00';
        return;
    }
    
    const total = expenses.reduce((sum, expense) => sum + expense.amount, 0);
    totalElement.textContent = total.toFixed(2);
    
    expensesList.innerHTML = expenses.map(expense => `
        <div class="expense-item">
            <div class="expense-info">
                <div class="expense-desc">${expense.description}</div>
                <div class="expense-details">Paid by ${expense.paidBy} on ${expense.date}</div>
            </div>
            <div class="expense-amount">₹${expense.amount.toFixed(2)}</div>
            <button class="delete-expense" onclick="deleteExpense(${expense.id})">Delete</button>
        </div>
    `).join('');
}

// Calculate who owes what
function calculateResults() {
    const resultsDiv = document.getElementById('results');
    
    if (people.length === 0 || expenses.length === 0) {
        resultsDiv.innerHTML = '<p class="empty-state">Add people and expenses to see the split!</p>';
        return;
    }
    
    const totalAmount = expenses.reduce((sum, expense) => sum + expense.amount, 0);
    const perPersonShare = totalAmount / people.length;
    
    // Calculate how much each person paid
    const personPaid = {};
    people.forEach(person => {
        personPaid[person] = 0;
    });
    
    expenses.forEach(expense => {
        personPaid[expense.paidBy] += expense.amount;
    });
    
    // Calculate balances (positive = owed money, negative = owes money)
    const balances = {};
    people.forEach(person => {
        balances[person] = personPaid[person] - perPersonShare;
    });
    
    // Create debt settlements
    const settlements = [];
    const creditors = []; // people who are owed money
    const debtors = []; // people who owe money
    
    Object.entries(balances).forEach(([person, balance]) => {
        if (balance > 0.01) {
            creditors.push({ person, amount: balance });
        } else if (balance < -0.01) {
            debtors.push({ person, amount: -balance });
        }
    });
    
    // Match debtors with creditors
    let i = 0, j = 0;
    while (i < debtors.length && j < creditors.length) {
        const debtor = debtors[i];
        const creditor = creditors[j];
        
        const amount = Math.min(debtor.amount, creditor.amount);
        
        settlements.push({
            from: debtor.person,
            to: creditor.person,
            amount: amount
        });
        
        debtor.amount -= amount;
        creditor.amount -= amount;
        
        if (debtor.amount < 0.01) i++;
        if (creditor.amount < 0.01) j++;
    }
    
    // Display results
    let html = `
        <div style="margin-bottom: 20px;">
            <strong>Total Expenses: ₹${totalAmount.toFixed(2)}</strong><br>
            <strong>Per Person: ₹${perPersonShare.toFixed(2)}</strong>
        </div>
    `;

    if (settlements.length === 0) {
        html += '<div class="debt-item">🎉 Everyone is even! No money needs to be exchanged.</div>';
    } else {
        html += '<div style="margin-bottom: 15px;"><strong>Settlements needed:</strong></div>';
        settlements.forEach(settlement => {
            html += `
                <div class="debt-item">
                    <div class="debt-amount">
                        ${settlement.from} owes ${settlement.to}
                        <strong>₹${settlement.amount.toFixed(2)}</strong>
                    </div>
                </div>
            `;
        });
    }
    
    resultsDiv.innerHTML = html;
}

// Clear all data
function clearAll() {
    if (confirm('Are you sure you want to clear all data? This cannot be undone.')) {
        people = [];
        expenses = [];
        updatePeopleDisplay();
        updateWhoPaidDropdown();
        updateExpensesDisplay();
        calculateResults();
    }
}

// Allow Enter key to add person
document.getElementById('personName').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        addPerson();
    }
});

// Allow Enter key to add expense (when amount field is focused)
document.getElementById('expenseAmount').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        addExpense();
    }
});

// Initialize
updatePeopleDisplay();
updateWhoPaidDropdown();
