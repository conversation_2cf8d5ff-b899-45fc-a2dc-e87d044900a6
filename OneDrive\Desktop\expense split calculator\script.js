let people = [], expenses = [];

function addPerson() {
    const name = document.getElementById('personName').value.trim();
    if (!name || people.includes(name)) return alert('Enter valid name!');
    people.push(name);
    document.getElementById('personName').value = '';
    render();
}

function addExpense() {
    const desc = document.getElementById('expenseDesc').value.trim();
    const amount = +document.getElementById('expenseAmount').value;
    const paidBy = document.getElementById('whoPaid').value;

    if (!desc || !amount || !paidBy) return alert('Fill all fields!');

    expenses.push({id: Date.now(), description: desc, amount, paidBy});
    document.getElementById('expenseDesc').value = '';
    document.getElementById('expenseAmount').value = '';
    document.getElementById('whoPaid').value = '';
    render();
}

function removePerson(name) {
    people = people.filter(p => p !== name);
    expenses = expenses.filter(e => e.paidBy !== name);
    render();
}

function deleteExpense(id) {
    expenses = expenses.filter(e => e.id !== id);
    render();
}

function clearAll() {
    if (confirm('Clear all data?')) {
        people = [];
        expenses = [];
        render();
    }
}

function render() {
    // People list
    document.getElementById('peopleList').innerHTML = people.length ?
        people.map(p => `<div class="person-tag">${p}<button class="remove-btn" onclick="removePerson('${p}')">×</button></div>`).join('') :
        '<p class="empty-state">No people added yet.</p>';

    // Who paid dropdown
    document.getElementById('whoPaid').innerHTML = '<option value="">Who paid?</option>' +
        people.map(p => `<option value="${p}">${p}</option>`).join('');

    // Expenses list
    const total = expenses.reduce((sum, e) => sum + e.amount, 0);
    document.getElementById('totalAmount').textContent = total.toFixed(2);
    document.getElementById('expensesList').innerHTML = expenses.length ?
        expenses.map(e => `
            <div class="expense-item">
                <div class="expense-info">
                    <div class="expense-desc">${e.description}</div>
                    <div class="expense-details">Paid by ${e.paidBy}</div>
                </div>
                <div class="expense-amount">₹${e.amount.toFixed(2)}</div>
                <button class="delete-expense" onclick="deleteExpense(${e.id})">Delete</button>
            </div>
        `).join('') : '<p class="empty-state">No expenses yet. Add one above!</p>';

    // Results
    if (!people.length || !expenses.length) {
        document.getElementById('results').innerHTML = '<p class="empty-state">Add people and expenses to see the split!</p>';
        return;
    }

    const perPerson = total / people.length;
    const paid = {};
    people.forEach(p => paid[p] = 0);
    expenses.forEach(e => paid[e.paidBy] += e.amount);

    const balances = {};
    people.forEach(p => balances[p] = paid[p] - perPerson);

    const creditors = [], debtors = [];
    Object.entries(balances).forEach(([p, b]) => {
        if (b > 0.01) creditors.push({person: p, amount: b});
        else if (b < -0.01) debtors.push({person: p, amount: -b});
    });

    const settlements = [];
    let i = 0, j = 0;
    while (i < debtors.length && j < creditors.length) {
        const amount = Math.min(debtors[i].amount, creditors[j].amount);
        settlements.push({from: debtors[i].person, to: creditors[j].person, amount});
        debtors[i].amount -= amount;
        creditors[j].amount -= amount;
        if (debtors[i].amount < 0.01) i++;
        if (creditors[j].amount < 0.01) j++;
    }

    document.getElementById('results').innerHTML = `
        <div style="margin-bottom: 20px;">
            <strong>Total: ₹${total.toFixed(2)} | Per Person: ₹${perPerson.toFixed(2)}</strong>
        </div>
        ${settlements.length ? settlements.map(s =>
            `<div class="debt-item">${s.from} owes ${s.to} <strong>₹${s.amount.toFixed(2)}</strong></div>`
        ).join('') : '<div class="debt-item">🎉 Everyone is even!</div>'}
    `;
}

// Event listeners
document.getElementById('personName').onkeypress = e => e.key === 'Enter' && addPerson();
document.getElementById('expenseAmount').onkeypress = e => e.key === 'Enter' && addExpense();

render();
