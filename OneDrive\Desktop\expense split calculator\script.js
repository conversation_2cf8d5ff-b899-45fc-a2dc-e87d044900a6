let people = [], expenses = [];

function addPerson() {
    console.log('addPerson called'); // Debug line
    const nameInput = document.getElementById('personName');
    const name = nameInput.value.trim();
    console.log('Name entered:', name); // Debug line

    if (name === '') {
        alert('Please enter a name!');
        return;
    }

    if (people.includes(name)) {
        alert('This person is already added!');
        return;
    }

    people.push(name);
    nameInput.value = '';
    console.log('People array:', people); // Debug line
    render();
}

function addExpense() {
    const desc = document.getElementById('expenseDesc').value.trim();
    const amount = +document.getElementById('expenseAmount').value;
    const paidBy = document.getElementById('whoPaid').value;

    if (!desc || !amount || !paidBy) return alert('Fill all fields!');

    expenses.push({id: Date.now(), description: desc, amount, paidBy});
    document.getElementById('expenseDesc').value = '';
    document.getElementById('expenseAmount').value = '';
    document.getElementById('whoPaid').value = '';
    render();
}

function removePerson(name) {
    people = people.filter(p => p !== name);
    expenses = expenses.filter(e => e.paidBy !== name);
    render();
}

function deleteExpense(id) {
    expenses = expenses.filter(e => e.id !== id);
    render();
}

function clearAll() {
    if (confirm('Clear all data?')) {
        people = [];
        expenses = [];
        render();
        showNotification('All data cleared!', 'info');
    }
}

// New Interactive Functions
function updateStats() {
    const total = expenses.reduce((sum, e) => sum + e.amount, 0);
    const avgPerPerson = people.length ? total / people.length : 0;
    const biggestExpense = expenses.length ? Math.max(...expenses.map(e => e.amount)) : 0;

    document.getElementById('totalPeople').textContent = people.length;
    document.getElementById('totalExpenses').textContent = expenses.length;
    document.getElementById('avgPerPerson').textContent = `₹${avgPerPerson.toFixed(0)}`;
    document.getElementById('biggestExpense').textContent = `₹${biggestExpense.toFixed(0)}`;
}

function addQuickExpense(desc, amount) {
    if (!people.length) {
        showNotification('Add people first!', 'warning');
        return;
    }

    // Auto-select first person or ask user
    const paidBy = people[0];
    expenses.push({
        id: Date.now(),
        description: desc,
        amount: amount,
        paidBy: paidBy
    });
    render();
    showNotification(`Added ${desc} for ₹${amount}`, 'success');
}

function splitEqually() {
    if (!expenses.length) {
        showNotification('No expenses to split!', 'warning');
        return;
    }

    const lastExpense = expenses[expenses.length - 1];
    const splitAmount = lastExpense.amount / people.length;
    showNotification(`Last expense split: ₹${splitAmount.toFixed(2)} per person`, 'info');
}

function editExpense(id) {
    const expense = expenses.find(e => e.id === id);
    if (!expense) return;

    const newDesc = prompt('Edit description:', expense.description);
    const newAmount = prompt('Edit amount:', expense.amount);

    if (newDesc && newAmount && !isNaN(newAmount)) {
        expense.description = newDesc;
        expense.amount = parseFloat(newAmount);
        render();
        showNotification('Expense updated!', 'success');
    }
}

function markSettled(from, to, amount) {
    if (confirm(`Mark ₹${amount.toFixed(2)} from ${from} to ${to} as settled?`)) {
        showNotification(`₹${amount.toFixed(2)} settled between ${from} and ${to}`, 'success');
        // Could add to a settled transactions list here
    }
}

function exportData() {
    const data = {
        people: people,
        expenses: expenses,
        total: expenses.reduce((sum, e) => sum + e.amount, 0),
        exportDate: new Date().toLocaleDateString()
    };

    const dataStr = JSON.stringify(data, null, 2);
    const blob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = 'expense-split.json';
    a.click();

    showNotification('Data exported!', 'success');
}

function shareResults() {
    const total = expenses.reduce((sum, e) => sum + e.amount, 0);
    const perPerson = people.length ? total / people.length : 0;

    const shareText = `💰 Expense Split Summary\n\nTotal: ₹${total.toFixed(2)}\nPeople: ${people.join(', ')}\nPer Person: ₹${perPerson.toFixed(2)}\n\nExpenses:\n${expenses.map(e => `• ${e.description}: ₹${e.amount} (${e.paidBy})`).join('\n')}`;

    if (navigator.share) {
        navigator.share({
            title: 'Expense Split',
            text: shareText
        });
    } else {
        navigator.clipboard.writeText(shareText);
        showNotification('Results copied to clipboard!', 'success');
    }
}

function settleUp() {
    if (confirm('Mark all debts as settled?')) {
        showNotification('All debts marked as settled! 🎉', 'success');
        // Could clear or mark expenses as settled
    }
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;

    // Add to page
    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => notification.classList.add('show'), 100);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => document.body.removeChild(notification), 300);
    }, 3000);
}

function render() {
    // People list
    document.getElementById('peopleList').innerHTML = people.length ?
        people.map(p => `<div class="person-tag">${p}<button class="remove-btn" onclick="removePerson('${p}')">×</button></div>`).join('') :
        '<p class="empty-state">No people added yet.</p>';

    // Who paid dropdown
    document.getElementById('whoPaid').innerHTML = '<option value="">Who paid?</option>' +
        people.map(p => `<option value="${p}">${p}</option>`).join('');

    // Expenses list
    const total = expenses.reduce((sum, e) => sum + e.amount, 0);
    document.getElementById('totalAmount').textContent = total.toFixed(2);
    document.getElementById('expensesList').innerHTML = expenses.length ?
        expenses.map(e => `
            <div class="expense-item" onclick="editExpense(${e.id})">
                <div class="expense-info">
                    <div class="expense-desc">${e.description}</div>
                    <div class="expense-details">Paid by ${e.paidBy}</div>
                </div>
                <div class="expense-amount">₹${e.amount.toFixed(2)}</div>
                <button class="delete-expense" onclick="event.stopPropagation(); deleteExpense(${e.id})">Delete</button>
            </div>
        `).join('') : '<p class="empty-state">No expenses yet. Add one above!</p>';

    // Update stats
    updateStats();

    // Results
    if (!people.length || !expenses.length) {
        document.getElementById('results').innerHTML = '<p class="empty-state">Add people and expenses to see the split!</p>';
        return;
    }

    const perPerson = total / people.length;
    const paid = {};
    people.forEach(p => paid[p] = 0);
    expenses.forEach(e => paid[e.paidBy] += e.amount);

    const balances = {};
    people.forEach(p => balances[p] = paid[p] - perPerson);

    const creditors = [], debtors = [];
    Object.entries(balances).forEach(([p, b]) => {
        if (b > 0.01) creditors.push({person: p, amount: b});
        else if (b < -0.01) debtors.push({person: p, amount: -b});
    });

    const settlements = [];
    let i = 0, j = 0;
    while (i < debtors.length && j < creditors.length) {
        const amount = Math.min(debtors[i].amount, creditors[j].amount);
        settlements.push({from: debtors[i].person, to: creditors[j].person, amount});
        debtors[i].amount -= amount;
        creditors[j].amount -= amount;
        if (debtors[i].amount < 0.01) i++;
        if (creditors[j].amount < 0.01) j++;
    }

    document.getElementById('results').innerHTML = `
        <div style="margin-bottom: 20px;">
            <strong>Total: ₹${total.toFixed(2)} | Per Person: ₹${perPerson.toFixed(2)}</strong>
        </div>
        ${settlements.length ? settlements.map(s =>
            `<div class="debt-item clickable" onclick="markSettled('${s.from}', '${s.to}', ${s.amount})">${s.from} owes ${s.to} <strong>₹${s.amount.toFixed(2)}</strong> <span class="settle-hint">Click to mark as settled</span></div>`
        ).join('') : '<div class="debt-item">🎉 Everyone is even!</div>'}
    `;
}

// Event listeners
document.getElementById('personName').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') addPerson();
});

document.getElementById('expenseAmount').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') addExpense();
});

render();
