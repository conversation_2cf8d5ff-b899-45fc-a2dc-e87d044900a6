* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 800px;
    margin: 0 auto;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.card h2 {
    margin-bottom: 20px;
    color: #333;
    font-size: 1.3rem;
}

/* Add Person Section */
.add-person {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.add-person input {
    flex: 1;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
}

.add-person input:focus {
    outline: none;
    border-color: #667eea;
}

.add-person button,
.expense-form button,
.clear-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: background 0.2s;
}

.add-person button:hover,
.expense-form button:hover {
    background: #5a6fd8;
}

.clear-btn {
    background: #e74c3c;
    margin-top: 15px;
}

.clear-btn:hover {
    background: #c0392b;
}

/* People List */
.people-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.person-tag {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 20px;
    padding: 8px 15px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.person-tag .remove-btn {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Expense Form */
.expense-form {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr auto;
    gap: 10px;
    align-items: end;
}

.expense-form input,
.expense-form select {
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
}

.expense-form input:focus,
.expense-form select:focus {
    outline: none;
    border-color: #667eea;
}

/* Expenses List */
.expense-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 10px;
    background: #f8f9fa;
}

.expense-info {
    flex: 1;
}

.expense-desc {
    font-weight: 600;
    margin-bottom: 5px;
}

.expense-details {
    color: #666;
    font-size: 14px;
}

.expense-amount {
    font-size: 1.2rem;
    font-weight: 700;
    color: #667eea;
}

.delete-expense {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 12px;
    cursor: pointer;
    margin-left: 15px;
}

.total-section {
    text-align: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 2px solid #e9ecef;
    font-size: 1.3rem;
    color: #333;
}

/* Results */
.results-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.results-card h2 {
    color: white;
}

.debt-item {
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    backdrop-filter: blur(10px);
}

.debt-amount {
    font-size: 1.1rem;
    font-weight: 600;
}

.empty-state {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 20px;
}

.results-card .empty-state {
    color: rgba(255,255,255,0.8);
}

/* Responsive */
@media (max-width: 768px) {
    .expense-form {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .add-person {
        flex-direction: column;
    }

    .expense-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .expense-amount {
        align-self: flex-end;
    }
}
