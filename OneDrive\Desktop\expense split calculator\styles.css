* { margin: 0; padding: 0; box-sizing: border-box; }

body {
    font-family: -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #1DB954, #191414, #1DB954);
    background-size: 400% 400%;
    animation: bg 10s ease infinite;
    min-height: 100vh;
    padding: 20px;
}

@keyframes bg {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.container { max-width: 800px; margin: 0 auto; }

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    font-weight: 900;
    margin-bottom: 10px;
}

.card {
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.2);
    transition: transform 0.3s;
}

.card:hover { transform: translateY(-5px); }

.card h2 {
    color: #191414;
    font-size: 1.3rem;
    margin-bottom: 20px;
}

.add-person, .expense-form {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.add-person input, .expense-form input, .expense-form select {
    flex: 1;
    padding: 12px 20px;
    border: 2px solid rgba(29,185,84,0.3);
    border-radius: 25px;
    font-size: 16px;
    background: white;
}

.add-person input:focus, .expense-form input:focus, .expense-form select:focus {
    outline: none;
    border-color: #1DB954;
    box-shadow: 0 0 15px rgba(29,185,84,0.3);
}

button {
    background: linear-gradient(45deg, #1DB954, #1ed760);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s;
}

button:hover { transform: translateY(-2px); }

.clear-btn {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    margin-top: 15px;
}

.people-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.person-tag {
    background: linear-gradient(45deg, #1DB954, #1ed760);
    color: white;
    border-radius: 20px;
    padding: 8px 15px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
}

.remove-btn {
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    padding: 0;
}

.expense-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border: 2px solid rgba(29,185,84,0.2);
    border-radius: 15px;
    margin-bottom: 10px;
    background: white;
    transition: all 0.3s;
}

.expense-item:hover {
    transform: translateY(-2px);
    border-color: rgba(29,185,84,0.4);
}

.expense-desc {
    font-weight: 700;
    color: #191414;
}

.expense-details {
    color: #666;
    font-size: 14px;
}

.expense-amount {
    font-size: 1.2rem;
    font-weight: 800;
    color: #1DB954;
    margin-right: 15px;
}

.delete-expense {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    padding: 8px 12px;
    border-radius: 10px;
}

.total-section {
    text-align: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 2px solid rgba(29,185,84,0.3);
    font-size: 1.3rem;
    color: #191414;
    font-weight: 800;
}

.results-card {
    background: linear-gradient(135deg, #1DB954, #191414);
    color: white;
}

.results-card h2 { color: white; }

.debt-item {
    background: rgba(255,255,255,0.15);
    border-radius: 15px;
    padding: 15px;
    margin-bottom: 10px;
    backdrop-filter: blur(20px);
}

.empty-state {
    text-align: center;
    color: #666;
    padding: 20px;
    font-style: italic;
}

.results-card .empty-state { color: rgba(255,255,255,0.8); }

/* New Interactive Styles */
.action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-top: 15px;
}

.export-btn, .share-btn, .settle-btn {
    background: linear-gradient(45deg, #3498db, #2980b9);
    padding: 10px 15px;
    border-radius: 20px;
    font-size: 14px;
}

.stats-card {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
}

.stat-item {
    text-align: center;
    background: rgba(255,255,255,0.1);
    padding: 15px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 800;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    opacity: 0.8;
}

.quick-actions .quick-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
}

.quick-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a52);
    padding: 12px;
    border-radius: 15px;
    font-size: 14px;
    text-align: center;
}

.clickable {
    cursor: pointer;
    transition: all 0.3s;
}

.clickable:hover {
    background: rgba(255,255,255,0.25);
    transform: scale(1.02);
}

.settle-hint {
    font-size: 12px;
    opacity: 0.7;
    display: block;
    margin-top: 5px;
}

.expense-item {
    cursor: pointer;
}

.expense-item:hover {
    background: rgba(29,185,84,0.1);
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 10px;
    color: white;
    font-weight: 600;
    transform: translateX(100%);
    transition: transform 0.3s;
    z-index: 1000;
    max-width: 300px;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: linear-gradient(45deg, #1DB954, #1ed760);
}

.notification.warning {
    background: linear-gradient(45deg, #f39c12, #e67e22);
}

.notification.info {
    background: linear-gradient(45deg, #3498db, #2980b9);
}

@media (max-width: 768px) {
    .expense-form { flex-direction: column; }
    .add-person { flex-direction: column; }
    .expense-item { flex-direction: column; align-items: flex-start; gap: 10px; }
    .action-buttons { flex-direction: column; }
    .stats-grid { grid-template-columns: repeat(2, 1fr); }
    .quick-buttons { grid-template-columns: 1fr; }
}
