* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Circular', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #1DB954 0%, #191414 25%, #1DB954 50%, #191414 75%, #1DB954 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 100vh;
    padding: 20px;
    color: #fff;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.container {
    max-width: 900px;
    margin: 0 auto;
}

header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

header h1 {
    font-size: 3rem;
    margin-bottom: 15px;
    font-weight: 900;
    background: linear-gradient(45deg, #1DB954, #1ed760);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: none;
    letter-spacing: -1px;
}

header p {
    font-size: 1.2rem;
    opacity: 0.85;
    font-weight: 400;
}

.card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.25);
}

.card h2 {
    margin-bottom: 25px;
    color: #191414;
    font-size: 1.5rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Add Person Section */
.add-person {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
}

.add-person input {
    flex: 1;
    padding: 15px 20px;
    border: 2px solid rgba(29, 185, 84, 0.3);
    border-radius: 50px;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.9);
    color: #191414;
    transition: all 0.3s ease;
}

.add-person input:focus {
    outline: none;
    border-color: #1DB954;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 0 20px rgba(29, 185, 84, 0.3);
}

.add-person button,
.expense-form button,
.clear-btn {
    background: linear-gradient(45deg, #1DB954, #1ed760);
    color: white;
    border: none;
    padding: 15px 25px;
    border-radius: 50px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(29, 185, 84, 0.4);
}

.add-person button:hover,
.expense-form button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(29, 185, 84, 0.6);
}

.clear-btn {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    margin-top: 20px;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);
}

.clear-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.6);
}

/* People List */
.people-list {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.person-tag {
    background: linear-gradient(45deg, #1DB954, #1ed760);
    color: white;
    border: none;
    border-radius: 25px;
    padding: 12px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(29, 185, 84, 0.3);
    transition: all 0.3s ease;
}

.person-tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(29, 185, 84, 0.4);
}

.person-tag .remove-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.person-tag .remove-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Expense Form */
.expense-form {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr auto;
    gap: 15px;
    align-items: end;
}

.expense-form input,
.expense-form select {
    padding: 15px 20px;
    border: 2px solid rgba(29, 185, 84, 0.3);
    border-radius: 15px;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.9);
    color: #191414;
    transition: all 0.3s ease;
}

.expense-form input:focus,
.expense-form select:focus {
    outline: none;
    border-color: #1DB954;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 0 20px rgba(29, 185, 84, 0.3);
}

/* Expenses List */
.expense-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border: 2px solid rgba(29, 185, 84, 0.2);
    border-radius: 15px;
    margin-bottom: 15px;
    background: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
}

.expense-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: rgba(29, 185, 84, 0.4);
}

.expense-info {
    flex: 1;
}

.expense-desc {
    font-weight: 700;
    margin-bottom: 8px;
    color: #191414;
    font-size: 1.1rem;
}

.expense-details {
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

.expense-amount {
    font-size: 1.4rem;
    font-weight: 800;
    color: #1DB954;
    margin-right: 15px;
}

.delete-expense {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
    border: none;
    border-radius: 10px;
    padding: 10px 15px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.delete-expense:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.5);
}

.total-section {
    text-align: center;
    margin-top: 25px;
    padding-top: 25px;
    border-top: 3px solid rgba(29, 185, 84, 0.3);
    font-size: 1.5rem;
    color: #191414;
    font-weight: 800;
}

/* Results */
.results-card {
    background: linear-gradient(135deg, #1DB954 0%, #191414 50%, #1DB954 100%);
    color: white;
    border: 2px solid rgba(29, 185, 84, 0.3);
}

.results-card h2 {
    color: white;
}

.debt-item {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 15px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.debt-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.debt-amount {
    font-size: 1.2rem;
    font-weight: 700;
}

.empty-state {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 25px;
    font-size: 1.1rem;
}

.results-card .empty-state {
    color: rgba(255, 255, 255, 0.8);
}

/* Responsive */
@media (max-width: 768px) {
    .expense-form {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .add-person {
        flex-direction: column;
    }

    .expense-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .expense-amount {
        align-self: flex-end;
    }
}
