/* CalcMaster Style Reset */
* { margin: 0; padding: 0; box-sizing: border-box; }

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
    background-size: 400% 400%;
    animation: gradientShift 20s ease infinite;
    color: #e2e8f0;
    line-height: 1.6;
    min-height: 100vh;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    25% { background-position: 100% 50%; }
    50% { background-position: 0% 100%; }
    75% { background-position: 100% 0%; }
    100% { background-position: 0% 50%; }
}

/* Header */
.main-header {
    background: rgba(15, 15, 35, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    position: sticky;
    top: 0;
    z-index: 100;

}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 3rem;
}

.logo {
    order: 2;
}

.logo h1 {
    font-size: 1.8rem;
    color: #2d3748;
    margin-bottom: 0.25rem;
    font-weight: 700;
}

.logo span {
    font-size: 0.875rem;
    color: #718096;
}

.main-nav {
    display: flex;
    gap: 2rem;
    order: 1;
}

.main-nav a {
    text-decoration: none;
    color: #4a5568;
    font-weight: 600;
    transition: all 0.3s;
    padding: 0.5rem 1rem;
    border-radius: 8px;
}

.main-nav a:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

/* Hero Section */
.hero-section {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 4rem 2rem;
    text-align: center;
    margin: 2rem;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
}

.hero-content h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.hero-content p {
    font-size: 1.125rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 2rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.8;
}

/* Calculator Section */
.calculator-section {
    padding: 2rem;
}

.calc-container {
    max-width: 1200px;
    margin: 0 auto;
}

.calc-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.calc-left, .calc-right {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* Cards */
.calc-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    transition: all 0.3s ease;
}

.calc-card:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    transform: translateY(-4px);
}

.card-header {
    padding: 1.5rem 1.5rem 0;
}

.card-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.card-subtitle {
    font-size: 0.875rem;
    color: #718096;
}

.card-body {
    padding: 1.5rem;
}

/* Form Elements */
.input-group {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.expense-form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
}

.expense-form-grid .full-width {
    grid-column: 1 / -1;
}

.calc-input, .calc-select {
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.875rem;
    transition: all 0.2s;
    background: white;
}

.calc-input:focus, .calc-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Buttons */
.btn-primary, .btn-secondary, .btn-success, .btn-danger {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.875rem;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #718096;
    color: white;
}

.btn-success {
    background: #718096;;
    color: white;
}

.btn-danger {
    background: #f56565;
    color: white;
}

.quick-btn {
    background: #edf2f7;
    color: #4a5568;
    border: 2px solid #e2e8f0;
    padding: 0.75rem;
    border-radius: 8px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
}

.quick-btn:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.people-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.person-tag {
    background: linear-gradient(45deg, #1DB954, #1ed760);
    color: white;
    border-radius: 20px;
    padding: 8px 15px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
}

.remove-btn {
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    padding: 0;
}

.expense-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border: 2px solid rgba(29,185,84,0.2);
    border-radius: 15px;
    margin-bottom: 10px;
    background: white;
    transition: all 0.3s;
}

.expense-item:hover {
    transform: translateY(-2px);
    border-color: rgba(29,185,84,0.4);
}

.expense-desc {
    font-weight: 700;
    color: #191414;
}

.expense-details {
    color: #666;
    font-size: 14px;
}

.expense-amount {
    font-size: 1.2rem;
    font-weight: 800;
    color: #1DB954;
    margin-right: 15px;
}

.delete-expense {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    padding: 8px 12px;
    border-radius: 10px;
}

.total-section {
    text-align: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 2px solid rgba(29,185,84,0.3);
    font-size: 1.3rem;
    color: #191414;
    font-weight: 800;
}

.results-card {
    background: rgba(30, 30, 50, 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #e2e8f0;
}

.results-card .card-header h3 {
    color: #e2e8f0;
}

.results-card .card-subtitle {
    color: #a0aec0;
}

.debt-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.debt-item:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
}

.empty-state {
    text-align: center;
    color: #666;
    padding: 20px;
    font-style: italic;
}

.results-card .empty-state { color: rgba(255,255,255,0.8); }

/* New Interactive Styles */
.action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-top: 15px;
}

.export-btn, .share-btn, .settle-btn {
    background: linear-gradient(45deg, #3498db, #2980b9);
    padding: 10px 15px;
    border-radius: 20px;
    font-size: 14px;
}

.stats-card {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
}

.stat-item {
    text-align: center;
    background: rgba(255,255,255,0.1);
    padding: 15px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 800;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    opacity: 0.8;
}

.quick-actions .quick-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
}

.quick-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a52);
    padding: 12px;
    border-radius: 15px;
    font-size: 14px;
    text-align: center;
}

.clickable {
    cursor: pointer;
    transition: all 0.3s;
}

.clickable:hover {
    background: rgba(255,255,255,0.25);
    transform: scale(1.02);
}

.settle-hint {
    font-size: 12px;
    opacity: 0.7;
    display: block;
    margin-top: 5px;
}

.expense-item {
    cursor: pointer;
}

.expense-item:hover {
    background: rgba(29,185,84,0.1);
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 10px;
    color: white;
    font-weight: 600;
    transform: translateX(100%);
    transition: transform 0.3s;
    z-index: 1000;
    max-width: 300px;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: linear-gradient(45deg, #1DB954, #1ed760);
}

.notification.warning {
    background: linear-gradient(45deg, #f39c12, #e67e22);
}

.notification.info {
    background: linear-gradient(45deg, #3498db, #2980b9);
}

/* Lists and Results */
.people-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.person-tag {
    background: #667eea;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.remove-btn {
    background: rgba(255,255,255,0.2);
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    cursor: pointer;
    font-size: 12px;
    color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: #f7fafc;
    border-radius: 8px;
}

.stat-item .stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.stat-item .stat-label {
    font-size: 0.75rem;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
}

.empty-state {
    text-align: center;
    color: #a0aec0;
    font-style: italic;
    padding: 2rem;
}

/* Features Section */
.features-section {
    padding: 4rem 2rem;
    background: white;
}

.features-container {
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
}

.features-container h2 {
    font-size: 2rem;
    margin-bottom: 3rem;
    color: #2d3748;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-card {
    padding: 2rem;
    text-align: center;
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    color: #2d3748;
}

.feature-card p {
    color: #718096;
}

/* Footer */
.main-footer {
    background: #2d3748;
    color: white;
    padding: 2rem;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-links a {
    color: #a0aec0;
    text-decoration: none;
}

.footer-bottom {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid #4a5568;
    color: #a0aec0;
}

/* Responsive */
@media (max-width: 1024px) {
    .calc-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    .header-container {
        justify-content: space-between;
        flex-direction: row;
    }
    .logo {
        order: 1;
    }
    .main-nav {
        order: 2;
    }
}

@media (max-width: 768px) {
    .calc-grid { grid-template-columns: 1fr; }
    .expense-form-grid { grid-template-columns: 1fr; }
    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }
    .hero-section {
        margin: 1rem;
        padding: 2rem 1rem;
    }
    .calculator-section {
        padding: 1rem;
    }
    .header-container {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    .logo {
        order: 1;
    }
    .main-nav {
        order: 2;
        justify-content: center;
    }
    .footer-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    .action-buttons { grid-template-columns: 1fr; }
    .quick-actions-grid { grid-template-columns: 1fr; }
    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

@media (max-width: 480px) {
    .hero-content h2 {
        font-size: 1.8rem;
    }
    .hero-content p {
        font-size: 1rem;
    }
    .stat-number {
        font-size: 1.5rem;
    }
    .calc-container {
        padding: 0 0.5rem;
    }
    .hero-section {
        margin: 0.5rem;
        padding: 1.5rem 1rem;
    }
}
