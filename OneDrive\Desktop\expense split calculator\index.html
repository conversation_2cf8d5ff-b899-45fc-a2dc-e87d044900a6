<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SplitMaster - Smart Expense Calculator</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Header -->
    <header class="main-header">
        <div class="header-container">
            <div class="logo">
                <h1>💰FairShare</h1>
                <span>Smart Expense Calculator</span>
            </div>
            <nav class="main-nav">
                <a href="#calculator">Calculator</a>
                <a href="#features">Features</a>
                <a href="#about">About</a>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="hero-container">
            <div class="hero-content">
                <h2>Split Bills Instantly with Friends</h2>
                <p>Calculate who owes what in seconds. No signup required - just add people, expenses, and get results!</p>
                <div class="hero-stats">
                    <div class="stat">
                        <span class="stat-number" id="heroTotalPeople">0</span>
                        <span class="stat-label">People Added</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number" id="heroTotalAmount">₹0</span>
                        <span class="stat-label">Total Amount</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number" id="heroExpenseCount">0</span>
                        <span class="stat-label">Expenses</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Calculator Section -->
    <main class="calculator-section" id="calculator">
        <div class="calc-container">
            <div class="calc-grid">
                <!-- Left Column - Input Forms -->
                <div class="calc-left">
                    <!-- Add People Card -->
                    <div class="calc-card">
                        <div class="card-header">
                            <h3>👥 Add People</h3>
                            <span class="card-subtitle">Who's splitting the bill?</span>
                        </div>
                        <div class="card-body">
                            <div class="input-group">
                                <input type="text" id="personName" placeholder="Enter name..." maxlength="20" class="calc-input">
                                <button onclick="addPerson()" class="btn-primary">Add Person</button>
                            </div>
                            <div class="people-list" id="peopleList">
                                <div class="empty-state">No people added yet</div>
                            </div>
                        </div>
                    </div>

                    <!-- Add Expense Card -->
                    <div class="calc-card">
                        <div class="card-header">
                            <h3>🧾 Add Expense</h3>
                            <span class="card-subtitle">What did you spend on?</span>
                        </div>
                        <div class="card-body">
                            <div class="expense-form-grid">
                                <input type="text" id="expenseDesc" placeholder="Description (e.g., Dinner)" maxlength="30" class="calc-input">
                                <input type="number" id="expenseAmount" placeholder="Amount (₹)" step="0.01" min="0" class="calc-input">
                                <select id="whoPaid" class="calc-select">
                                    <option value="">Who paid?</option>
                                </select>
                                <button onclick="addExpense()" class="btn-primary full-width">Add Expense</button>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="calc-card">
                        <div class="card-header">
                            <h3>⚡ Quick Add</h3>
                            <span class="card-subtitle">Common expenses</span>
                        </div>
                        <div class="card-body">
                            <div class="quick-actions-grid">
                                <button onclick="addQuickExpense('Food', 500)" class="quick-btn">🍕 Food ₹500</button>
                                <button onclick="addQuickExpense('Transport', 200)" class="quick-btn">🚗 Uber ₹200</button>
                                <button onclick="addQuickExpense('Movie', 300)" class="quick-btn">🎬 Movie ₹300</button>
                                <button onclick="addQuickExpense('Coffee', 150)" class="quick-btn">☕ Coffee ₹150</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column - Results -->
                <div class="calc-right">
                    <!-- Live Stats -->
                    <div class="calc-card stats-card">
                        <div class="card-header">
                            <h3>📊 Live Stats</h3>
                            <span class="card-subtitle">Real-time calculations</span>
                        </div>
                        <div class="card-body">
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-number" id="totalPeople">0</div>
                                    <div class="stat-label">People</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number" id="totalExpenses">0</div>
                                    <div class="stat-label">Expenses</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number" id="totalAmount">₹0</div>
                                    <div class="stat-label">Total</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number" id="avgPerPerson">₹0</div>
                                    <div class="stat-label">Per Person</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Expenses List -->
                    <div class="calc-card">
                        <div class="card-header">
                            <h3>📋 Expenses</h3>
                            <span class="card-subtitle">All your expenses</span>
                        </div>
                        <div class="card-body">
                            <div class="expenses-list" id="expensesList">
                                <div class="empty-state">No expenses yet. Add one to get started!</div>
                            </div>
                        </div>
                    </div>

                    <!-- Results -->
                    <div class="calc-card results-card">
                        <div class="card-header">
                            <h3>💸 Who Owes What</h3>
                            <span class="card-subtitle">Settlement calculations</span>
                        </div>
                        <div class="card-body">
                            <div id="results">
                                <div class="empty-state">Add people and expenses to see the split!</div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="calc-card">
                        <div class="card-body">
                            <div class="action-buttons">
                                <button onclick="exportData()" class="btn-secondary">📊 Export</button>
                                <button onclick="shareResults()" class="btn-secondary">📱 Share</button>
                                <button onclick="settleUp()" class="btn-success">✅ Settle</button>
                                <button onclick="clearAll()" class="btn-danger">🗑️ Clear</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Features Section -->
    <section class="features-section" id="features">
        <div class="features-container">
            <h2>Why Choose SplitMaster?</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h3>Lightning Fast</h3>
                    <p>Calculate splits in seconds with our optimized algorithms</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔒</div>
                    <h3>No Signup Required</h3>
                    <p>Start splitting immediately - no accounts or personal data needed</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <h3>Mobile Friendly</h3>
                    <p>Works perfectly on all devices - phone, tablet, or desktop</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🎯</div>
                    <h3>Smart Calculations</h3>
                    <p>Minimizes transactions and handles complex splitting scenarios</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="main-footer">
        <div class="footer-container">
            <div class="footer-content">
                <div class="footer-brand">
                    <h3>💰 SplitMaster</h3>
                    <p>The smartest way to split expenses with friends</p>
                </div>
                <div class="footer-links">
                    <a href="#about">About</a>
                    <a href="#privacy">Privacy</a>
                    <a href="#contact">Contact</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 FairShare for easy expense splitting.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
