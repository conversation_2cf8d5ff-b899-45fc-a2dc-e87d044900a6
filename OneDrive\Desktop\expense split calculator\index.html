<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Split It - Expense Calculator</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1> Split It</h1>
            <p>Easy expense splitting with friends</p>
        </header>

        <main>
                        <section class="card">
                <h2>👥 Add People</h2>
                <div class="add-person">
                    <input type="text" id="personName" placeholder="Enter name..." maxlength="20">
                    <button onclick="addPerson()">Add</button>
                </div>
                <div class="people-list" id="peopleList">
                    <!-- People will appear here -->
                </div>
            </section>

            <!-- Add Expense Section -->
            <section class="card">
                <h2>🧾 Add Expense</h2>
                <div class="expense-form">
                    <input type="text" id="expenseDesc" placeholder="What was it for?" maxlength="30">
                    <input type="number" id="expenseAmount" placeholder="Amount (₹)" step="0.01" min="0">
                    <select id="whoPaid">
                        <option value="">Who paid?</option>
                    </select>
                    <button onclick="addExpense()">Add Expense</button>
                </div>
            </section>

            <!-- Expenses List -->
            <section class="card">
                <h2>📋 Expenses</h2>
                <div class="expenses-list" id="expensesList">
                    <p class="empty-state">No expenses yet. Add one above!</p>
                </div>
                <div class="total-section">
                    <strong>Total: ₹<span id="totalAmount">0.00</span></strong>
                </div>
            </section>

            <!-- Results Section -->
            <section class="card results-card">
                <h2> Who Owes What</h2>
                <div id="results">
                    <p class="empty-state">Add people and expenses to see the split!</p>
                </div>

                <!-- Interactive Actions -->
                <div class="action-buttons">
                    <button onclick="exportData()" class="export-btn">📊 Export Summary</button>
                    <button onclick="shareResults()" class="share-btn">📱 Share Results</button>
                    <button onclick="settleUp()" class="settle-btn">✅ Mark as Settled</button>
                    <button onclick="clearAll()" class="clear-btn">🗑️ Clear All</button>
                </div>
            </section>

            <!-- Quick Stats -->
            <section class="card stats-card">
                <h2>📈 Quick Stats</h2>
                <div class="stats-grid" id="statsGrid">
                    <div class="stat-item">
                        <div class="stat-number" id="totalPeople">0</div>
                        <div class="stat-label">People</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="totalExpenses">0</div>
                        <div class="stat-label">Expenses</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="avgPerPerson">₹0</div>
                        <div class="stat-label">Avg per Person</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="biggestExpense">₹0</div>
                        <div class="stat-label">Biggest Expense</div>
                    </div>
                </div>
            </section>

            <!-- Quick Actions -->
            <section class="card quick-actions">
                <h2>⚡ Quick Actions</h2>
                <div class="quick-buttons">
                    <button onclick="addQuickExpense('Food', 500)" class="quick-btn">🍕 Food ₹500</button>
                    <button onclick="addQuickExpense('Transport', 200)" class="quick-btn">🚗 Transport ₹200</button>
                    <button onclick="addQuickExpense('Movie', 300)" class="quick-btn">🎬 Movie ₹300</button>
                    <button onclick="splitEqually()" class="quick-btn">⚖️ Split Last Equally</button>
                </div>
            </section>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>
