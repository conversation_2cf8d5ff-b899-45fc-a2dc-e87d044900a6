<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Expense Split Calculator</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">
                    <i class="fas fa-calculator"></i>
                    Expense Split Calculator
                </h1>
                <div class="header-controls">
                    <button class="theme-toggle" id="themeToggle">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button class="settings-btn" id="settingsBtn">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Navigation Tabs -->
            <nav class="tab-navigation">
                <button class="tab-btn active" data-tab="expenses">
                    <i class="fas fa-receipt"></i>
                    Expenses
                </button>
                <button class="tab-btn" data-tab="participants">
                    <i class="fas fa-users"></i>
                    Participants
                </button>
                <button class="tab-btn" data-tab="results">
                    <i class="fas fa-chart-pie"></i>
                    Results
                </button>
                <button class="tab-btn" data-tab="history">
                    <i class="fas fa-history"></i>
                    History
                </button>
            </nav>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Expenses Tab -->
                <div class="tab-panel active" id="expenses-panel">
                    <div class="panel-header">
                        <h2>Add Expense</h2>
                        <button class="btn-primary" id="addExpenseBtn">
                            <i class="fas fa-plus"></i>
                            New Expense
                        </button>
                    </div>
                    
                    <div class="expense-form-container">
                        <form class="expense-form" id="expenseForm">
                            <div class="form-group">
                                <label for="expenseTitle">Expense Title</label>
                                <input type="text" id="expenseTitle" placeholder="e.g., Dinner at Restaurant" required>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="expenseAmount">Amount</label>
                                    <div class="input-with-currency">
                                        <span class="currency-symbol">$</span>
                                        <input type="number" id="expenseAmount" placeholder="0.00" step="0.01" min="0" required>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="expenseCategory">Category</label>
                                    <select id="expenseCategory">
                                        <option value="food">🍽️ Food & Dining</option>
                                        <option value="transport">🚗 Transportation</option>
                                        <option value="entertainment">🎬 Entertainment</option>
                                        <option value="shopping">🛍️ Shopping</option>
                                        <option value="utilities">⚡ Utilities</option>
                                        <option value="other">📦 Other</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="expenseDate">Date</label>
                                    <input type="date" id="expenseDate" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="paidBy">Paid By</label>
                                    <select id="paidBy" required>
                                        <option value="">Select participant</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label>Split Method</label>
                                <div class="split-method-options">
                                    <label class="radio-option">
                                        <input type="radio" name="splitMethod" value="equal" checked>
                                        <span class="radio-custom"></span>
                                        Equal Split
                                    </label>
                                    <label class="radio-option">
                                        <input type="radio" name="splitMethod" value="percentage">
                                        <span class="radio-custom"></span>
                                        Percentage
                                    </label>
                                    <label class="radio-option">
                                        <input type="radio" name="splitMethod" value="custom">
                                        <span class="radio-custom"></span>
                                        Custom Amount
                                    </label>
                                </div>
                            </div>
                            
                            <div class="participants-split" id="participantsSplit">
                                <!-- Dynamic participant split options will be inserted here -->
                            </div>
                            
                            <div class="form-actions">
                                <button type="button" class="btn-secondary" id="cancelExpense">Cancel</button>
                                <button type="submit" class="btn-primary">
                                    <i class="fas fa-save"></i>
                                    Save Expense
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <div class="expenses-list" id="expensesList">
                        <!-- Expenses will be dynamically added here -->
                    </div>
                </div>

                <!-- Participants Tab -->
                <div class="tab-panel" id="participants-panel">
                    <div class="panel-header">
                        <h2>Manage Participants</h2>
                        <button class="btn-primary" id="addParticipantBtn">
                            <i class="fas fa-user-plus"></i>
                            Add Participant
                        </button>
                    </div>
                    
                    <div class="participant-form-container" style="display: none;">
                        <form class="participant-form" id="participantForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="participantName">Name</label>
                                    <input type="text" id="participantName" placeholder="Enter participant name" required>
                                </div>
                                <div class="form-group">
                                    <label for="participantEmail">Email (Optional)</label>
                                    <input type="email" id="participantEmail" placeholder="<EMAIL>">
                                </div>
                            </div>
                            
                            <div class="form-actions">
                                <button type="button" class="btn-secondary" id="cancelParticipant">Cancel</button>
                                <button type="submit" class="btn-primary">
                                    <i class="fas fa-save"></i>
                                    Add Participant
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <div class="participants-grid" id="participantsGrid">
                        <!-- Participants will be dynamically added here -->
                    </div>
                </div>

                <!-- Results Tab -->
                <div class="tab-panel" id="results-panel">
                    <div class="results-summary" id="resultsSummary">
                        <!-- Results summary will be displayed here -->
                    </div>
                    
                    <div class="debt-settlement" id="debtSettlement">
                        <!-- Debt settlement calculations will be displayed here -->
                    </div>
                    
                    <div class="export-options">
                        <button class="btn-secondary" id="exportBtn">
                            <i class="fas fa-download"></i>
                            Export Summary
                        </button>
                        <button class="btn-secondary" id="shareBtn">
                            <i class="fas fa-share"></i>
                            Share Results
                        </button>
                    </div>
                </div>

                <!-- History Tab -->
                <div class="tab-panel" id="history-panel">
                    <div class="panel-header">
                        <h2>Expense History</h2>
                        <div class="history-filters">
                            <select id="categoryFilter">
                                <option value="">All Categories</option>
                                <option value="food">Food & Dining</option>
                                <option value="transport">Transportation</option>
                                <option value="entertainment">Entertainment</option>
                                <option value="shopping">Shopping</option>
                                <option value="utilities">Utilities</option>
                                <option value="other">Other</option>
                            </select>
                            <input type="month" id="monthFilter">
                        </div>
                    </div>
                    
                    <div class="history-timeline" id="historyTimeline">
                        <!-- Timeline will be dynamically generated -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modals -->
    <div class="modal-overlay" id="modalOverlay">
        <div class="modal" id="settingsModal">
            <div class="modal-header">
                <h3>Settings</h3>
                <button class="modal-close" id="closeModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                <div class="setting-group">
                    <label for="currencySelect">Currency</label>
                    <select id="currencySelect">
                        <option value="USD">$ USD</option>
                        <option value="EUR">€ EUR</option>
                        <option value="GBP">£ GBP</option>
                        <option value="INR">₹ INR</option>
                    </select>
                </div>
                
                <div class="setting-group">
                    <label>
                        <input type="checkbox" id="autoSave">
                        Auto-save expenses
                    </label>
                </div>
                
                <div class="setting-group">
                    <label>
                        <input type="checkbox" id="showNotifications">
                        Show notifications
                    </label>
                </div>
            </div>
            <div class="modal-actions">
                <button class="btn-secondary" id="resetData">Reset All Data</button>
                <button class="btn-primary" id="saveSettings">Save Settings</button>
            </div>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div class="loading-spinner" id="loadingSpinner">
        <div class="spinner"></div>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toastContainer">
        <!-- Toast notifications will be dynamically added here -->
    </div>

    <script src="script.js"></script>
</body>
</html>
